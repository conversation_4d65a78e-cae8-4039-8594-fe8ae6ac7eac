#!/usr/bin/env node

// Script để debug và test Gemini API trong VSCode
console.log('🔧 Gemini API Debug Script for VSCode');
console.log('=====================================');
console.log('Copy and paste this script into VSCode Developer Console (F12 → Console):\n');

const debugScript = `
// 🔧 Gemini API Debug Script
console.log('🚀 Starting Gemini API Debug...');

// Step 1: Check extension
function checkExtension() {
    console.log('\\n📦 Step 1: Checking Extension');
    console.log('==============================');
    
    const extension = vscode.extensions.getExtension('geminipro.vscode-gemini-pro');
    if (extension) {
        console.log('✅ Extension found:', extension.id);
        console.log('✅ Version:', extension.packageJSON.version);
        console.log('✅ Active:', extension.isActive);
        return extension;
    } else {
        console.log('❌ Extension not found');
        return null;
    }
}

// Step 2: Check configuration
function checkConfiguration() {
    console.log('\\n⚙️ Step 2: Checking Configuration');
    console.log('==================================');
    
    const config = vscode.workspace.getConfiguration('geminiPro');
    
    const provider = config.get('ai.provider');
    const geminiApiKey = config.get('ai.geminiApiKey');
    const claudeApiKey = config.get('ai.claudeApiKey');
    const maxTokens = config.get('ai.maxTokens');
    const temperature = config.get('ai.temperature');
    
    console.log('- Provider:', provider);
    console.log('- Gemini API Key:', geminiApiKey ? \`\${geminiApiKey.substring(0, 10)}...\` : 'Not set');
    console.log('- Claude API Key:', claudeApiKey ? \`\${claudeApiKey.substring(0, 10)}...\` : 'Not set');
    console.log('- Max Tokens:', maxTokens);
    console.log('- Temperature:', temperature);
    
    // Validate API key format
    if (geminiApiKey) {
        if (geminiApiKey.startsWith('AIza')) {
            console.log('✅ Gemini API key format looks correct');
        } else {
            console.log('⚠️ Gemini API key should start with "AIza"');
        }
        
        if (geminiApiKey.length >= 35) {
            console.log('✅ Gemini API key length looks correct');
        } else {
            console.log('⚠️ Gemini API key seems too short');
        }
    } else {
        console.log('❌ Gemini API key not configured');
    }
    
    return {
        provider,
        geminiApiKey,
        claudeApiKey,
        maxTokens,
        temperature
    };
}

// Step 3: Test API directly
async function testGeminiAPI(apiKey) {
    console.log('\\n🧪 Step 3: Testing Gemini API Directly');
    console.log('=======================================');
    
    if (!apiKey) {
        console.log('❌ No API key to test');
        return false;
    }
    
    try {
        // Test 1: List models
        console.log('🔍 Testing API connection...');
        const modelsResponse = await fetch(\`https://generativelanguage.googleapis.com/v1beta/models?key=\${apiKey}\`);
        const modelsData = await modelsResponse.json();
        
        if (modelsResponse.ok) {
            console.log('✅ API connection successful');
            console.log(\`📋 Available models: \${modelsData.models?.length || 0}\`);
            
            // Test 2: Simple generation
            console.log('🔍 Testing content generation...');
            const generateResponse = await fetch(\`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=\${apiKey}\`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: 'Say hello in one word'
                        }]
                    }]
                })
            });
            
            const generateData = await generateResponse.json();
            
            if (generateResponse.ok) {
                console.log('✅ Content generation successful');
                console.log('📝 Response:', generateData.candidates?.[0]?.content?.parts?.[0]?.text || 'No response');
                return true;
            } else {
                console.log('❌ Content generation failed:', generateData.error?.message || 'Unknown error');
                return false;
            }
        } else {
            console.log('❌ API connection failed:', modelsData.error?.message || 'Unknown error');
            return false;
        }
    } catch (error) {
        console.log('❌ Network error:', error.message);
        return false;
    }
}

// Step 4: Test extension's AI provider
async function testExtensionAI() {
    console.log('\\n🔧 Step 4: Testing Extension AI Provider');
    console.log('=========================================');
    
    try {
        // Try to access extension's internal API
        const extension = vscode.extensions.getExtension('geminipro.vscode-gemini-pro');
        if (!extension || !extension.isActive) {
            console.log('❌ Extension not active');
            return false;
        }
        
        // Check if extension exports any API
        if (extension.exports) {
            console.log('✅ Extension exports found');
            console.log('📋 Available exports:', Object.keys(extension.exports));
        } else {
            console.log('⚠️ No extension exports found');
        }
        
        return true;
    } catch (error) {
        console.log('❌ Error testing extension AI:', error.message);
        return false;
    }
}

// Step 5: Test chat command
async function testChatCommand() {
    console.log('\\n💬 Step 5: Testing Chat Command');
    console.log('================================');
    
    try {
        // Try to execute chat command
        await vscode.commands.executeCommand('vscode-gemini-pro.openChat');
        console.log('✅ Chat command executed successfully');
        return true;
    } catch (error) {
        console.log('❌ Chat command failed:', error.message);
        return false;
    }
}

// Main debug function
async function runDebug() {
    console.log('🚀 Running Complete Gemini API Debug...\\n');
    
    // Step 1: Check extension
    const extension = checkExtension();
    if (!extension) {
        console.log('\\n❌ Cannot continue without extension');
        return;
    }
    
    // Step 2: Check configuration
    const config = checkConfiguration();
    
    // Step 3: Test API directly
    const apiWorking = await testGeminiAPI(config.geminiApiKey);
    
    // Step 4: Test extension AI
    const extensionWorking = await testExtensionAI();
    
    // Step 5: Test chat command
    const chatWorking = await testChatCommand();
    
    // Summary
    console.log('\\n📊 Debug Summary');
    console.log('================');
    console.log('Extension Found:', extension ? '✅' : '❌');
    console.log('API Key Configured:', config.geminiApiKey ? '✅' : '❌');
    console.log('API Working:', apiWorking ? '✅' : '❌');
    console.log('Extension AI:', extensionWorking ? '✅' : '❌');
    console.log('Chat Command:', chatWorking ? '✅' : '❌');
    
    // Recommendations
    console.log('\\n💡 Recommendations');
    console.log('==================');
    
    if (!config.geminiApiKey) {
        console.log('1. Configure Gemini API key in settings');
        console.log('   - Open Settings (Ctrl+,)');
        console.log('   - Search for "geminiPro.ai.geminiApiKey"');
        console.log('   - Enter your API key');
    }
    
    if (config.geminiApiKey && !apiWorking) {
        console.log('1. Check your API key is correct');
        console.log('2. Ensure Generative AI API is enabled in Google Cloud');
        console.log('3. Check network connection');
    }
    
    if (apiWorking && !extensionWorking) {
        console.log('1. Try reloading VSCode window');
        console.log('2. Check extension logs in Output panel');
    }
    
    console.log('\\n✨ Debug completed!');
}

// Run the debug
runDebug();
`;

console.log(debugScript);

console.log('\n🎯 Quick Fix Commands:');
console.log('======================');
console.log('If API key is not working, try these in VSCode:');
console.log('');
console.log('1. Check current API key:');
console.log('   vscode.workspace.getConfiguration("geminiPro").get("ai.geminiApiKey")');
console.log('');
console.log('2. Set API key programmatically:');
console.log('   vscode.workspace.getConfiguration("geminiPro").update("ai.geminiApiKey", "YOUR_API_KEY", vscode.ConfigurationTarget.Global)');
console.log('');
console.log('3. Reload window:');
console.log('   vscode.commands.executeCommand("workbench.action.reloadWindow")');
console.log('');
console.log('4. Open settings:');
console.log('   vscode.commands.executeCommand("workbench.action.openSettings", "geminiPro")');
