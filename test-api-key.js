#!/usr/bin/env node

// Script để test API key Gemini
const { GoogleGenerativeAI } = require('@google/generative-ai');

async function testGeminiAPI() {
    console.log('🔑 Testing Gemini API Key...\n');
    
    // Lấy API key từ environment hoặc input
    const apiKey = process.argv[2];
    
    if (!apiKey) {
        console.log('❌ Please provide API key as argument:');
        console.log('   node test-api-key.js YOUR_API_KEY');
        console.log('\n💡 Or test in VSCode console:');
        console.log('   1. Open VSCode Developer Console (F12)');
        console.log('   2. Paste this code:');
        console.log(`
// Test Gemini API in VSCode
async function testGeminiInVSCode() {
    const config = vscode.workspace.getConfiguration('geminiPro');
    const apiKey = config.get('ai.geminiApiKey');
    
    if (!apiKey) {
        console.log('❌ No API key configured');
        return;
    }
    
    console.log('🔑 API Key found:', apiKey.substring(0, 10) + '...');
    
    try {
        // Test API call
        const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models?key=' + apiKey);
        const data = await response.json();
        
        if (response.ok) {
            console.log('✅ API Key is valid');
            console.log('📋 Available models:', data.models?.length || 0);
        } else {
            console.log('❌ API Error:', data.error?.message || 'Unknown error');
        }
    } catch (error) {
        console.log('❌ Network Error:', error.message);
    }
}

testGeminiInVSCode();
        `);
        return;
    }
    
    console.log('🔑 API Key:', apiKey.substring(0, 10) + '...');
    
    try {
        // Test 1: Check if API key format is correct
        if (!apiKey.startsWith('AIza')) {
            console.log('⚠️  Warning: API key should start with "AIza"');
        }
        
        // Test 2: Initialize Gemini AI
        const genAI = new GoogleGenerativeAI(apiKey);
        console.log('✅ GoogleGenerativeAI initialized');
        
        // Test 3: Get model
        const model = genAI.getGenerativeModel({ model: "gemini-pro" });
        console.log('✅ Model obtained: gemini-pro');
        
        // Test 4: Simple test prompt
        console.log('🧪 Testing with simple prompt...');
        const result = await model.generateContent("Hello, say hi back!");
        const response = await result.response;
        const text = response.text();
        
        console.log('✅ API Response:', text);
        console.log('\n🎉 API Key is working correctly!');
        
    } catch (error) {
        console.log('❌ Error testing API:', error.message);
        
        if (error.message.includes('API_KEY_INVALID')) {
            console.log('💡 Solution: Check if your API key is correct');
        } else if (error.message.includes('PERMISSION_DENIED')) {
            console.log('💡 Solution: Enable Generative AI API in Google Cloud Console');
        } else if (error.message.includes('QUOTA_EXCEEDED')) {
            console.log('💡 Solution: Check your API quota limits');
        } else {
            console.log('💡 Solution: Check network connection and API key');
        }
    }
}

// Run test
testGeminiAPI();
