import * as vscode from 'vscode';
import { Logger } from '../../utils/Logger';

export interface TokenUsage {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
}

export interface CostCalculation {
    inputCost: number;
    outputCost: number;
    totalCost: number;
    currency: string;
}

export interface UsageRecord {
    id: string;
    timestamp: string;
    provider: 'gemini' | 'claude' | 'custom';
    model: string;
    operation: 'chat' | 'completion' | 'nextEdit' | 'instruction';
    usage: TokenUsage;
    cost: CostCalculation;
    metadata?: {
        userId?: string;
        sessionId?: string;
        feature?: string;
    };
}

export interface BillingPeriod {
    startDate: string;
    endDate: string;
    totalCost: number;
    totalTokens: number;
    recordCount: number;
}

export interface CostAlert {
    id: string;
    type: 'daily' | 'weekly' | 'monthly' | 'total';
    threshold: number;
    currentAmount: number;
    triggered: boolean;
    lastTriggered?: string;
}

export class CostTracker {
    private usageRecords: UsageRecord[] = [];
    private costAlerts: CostAlert[] = [];
    private readonly maxRecords = 10000; // Limit storage

    // Pricing per 1M tokens (USD)
    private readonly pricing = {
        gemini: {
            'gemini-2.0-flash': { input: 0.10, output: 0.40 },
            'gemini-2.5-flash': { input: 0.15, output: 0.60 },
            'gemini-2.5-pro': { input: 1.25, output: 10.00 },
            'gemini-1.5-flash': { input: 0.075, output: 0.30 },
            'gemini-1.5-pro': { input: 1.25, output: 5.00 }
        },
        claude: {
            'claude-4-opus': { input: 15.00, output: 75.00 },
            'claude-4-sonnet': { input: 3.00, output: 15.00 },
            'claude-3.5-haiku': { input: 0.80, output: 4.00 },
            'claude-3-opus': { input: 15.00, output: 75.00 },
            'claude-3-sonnet': { input: 3.00, output: 15.00 },
            'claude-3-haiku': { input: 0.25, output: 1.25 }
        }
    };

    constructor(private context: vscode.ExtensionContext) {
        this.loadUsageRecords();
        this.loadCostAlerts();
        this.setupPeriodicCleanup();
    }

    public trackUsage(
        provider: 'gemini' | 'claude' | 'custom',
        model: string,
        operation: UsageRecord['operation'],
        usage: TokenUsage,
        metadata?: UsageRecord['metadata']
    ): UsageRecord {
        const cost = this.calculateCost(provider, model, usage);
        
        const record: UsageRecord = {
            id: this.generateRecordId(),
            timestamp: new Date().toISOString(),
            provider,
            model,
            operation,
            usage,
            cost,
            metadata
        };

        this.usageRecords.unshift(record);
        
        // Limit storage
        if (this.usageRecords.length > this.maxRecords) {
            this.usageRecords = this.usageRecords.slice(0, this.maxRecords);
        }

        this.saveUsageRecords();
        this.checkCostAlerts(record);
        
        Logger.info(`Cost tracked: ${provider}/${model} - $${cost.totalCost.toFixed(4)}`);
        
        return record;
    }

    public calculateCost(
        provider: 'gemini' | 'claude' | 'custom',
        model: string,
        usage: TokenUsage
    ): CostCalculation {
        if (provider === 'custom') {
            return {
                inputCost: 0,
                outputCost: 0,
                totalCost: 0,
                currency: 'USD'
            };
        }

        const modelPricing = (this.pricing as any)[provider]?.[model];
        if (!modelPricing) {
            Logger.warn(`No pricing found for ${provider}/${model}`);
            return {
                inputCost: 0,
                outputCost: 0,
                totalCost: 0,
                currency: 'USD'
            };
        }

        const inputCost = (usage.inputTokens / 1000000) * modelPricing.input;
        const outputCost = (usage.outputTokens / 1000000) * modelPricing.output;
        const totalCost = inputCost + outputCost;

        return {
            inputCost,
            outputCost,
            totalCost,
            currency: 'USD'
        };
    }

    public getDailyUsage(date?: string): BillingPeriod {
        const targetDate = date || new Date().toISOString().split('T')[0];
        const records = this.usageRecords.filter(record => 
            record.timestamp.startsWith(targetDate)
        );

        return this.aggregateRecords(records, targetDate, targetDate);
    }

    public getWeeklyUsage(weekStart?: string): BillingPeriod {
        const start = weekStart || this.getWeekStart();
        const end = this.getWeekEnd(start);
        
        const records = this.usageRecords.filter(record => {
            const recordDate = record.timestamp.split('T')[0];
            return recordDate >= start && recordDate <= end;
        });

        return this.aggregateRecords(records, start, end);
    }

    public getMonthlyUsage(month?: string): BillingPeriod {
        const targetMonth = month || new Date().toISOString().substring(0, 7);
        const records = this.usageRecords.filter(record => 
            record.timestamp.startsWith(targetMonth)
        );

        const start = `${targetMonth}-01`;
        const end = this.getMonthEnd(targetMonth);

        return this.aggregateRecords(records, start, end);
    }

    public getTotalUsage(): BillingPeriod {
        if (this.usageRecords.length === 0) {
            const today = new Date().toISOString().split('T')[0];
            return {
                startDate: today,
                endDate: today,
                totalCost: 0,
                totalTokens: 0,
                recordCount: 0
            };
        }

        const sortedRecords = [...this.usageRecords].sort((a, b) => 
            a.timestamp.localeCompare(b.timestamp)
        );

        const start = sortedRecords[0].timestamp.split('T')[0];
        const end = sortedRecords[sortedRecords.length - 1].timestamp.split('T')[0];

        return this.aggregateRecords(this.usageRecords, start, end);
    }

    public getUsageByProvider(): Record<string, BillingPeriod> {
        const providers = ['gemini', 'claude', 'custom'];
        const result: Record<string, BillingPeriod> = {};

        for (const provider of providers) {
            const records = this.usageRecords.filter(r => r.provider === provider);
            if (records.length > 0) {
                const sortedRecords = [...records].sort((a, b) => 
                    a.timestamp.localeCompare(b.timestamp)
                );
                const start = sortedRecords[0].timestamp.split('T')[0];
                const end = sortedRecords[sortedRecords.length - 1].timestamp.split('T')[0];
                result[provider] = this.aggregateRecords(records, start, end);
            } else {
                const today = new Date().toISOString().split('T')[0];
                result[provider] = {
                    startDate: today,
                    endDate: today,
                    totalCost: 0,
                    totalTokens: 0,
                    recordCount: 0
                };
            }
        }

        return result;
    }

    public getUsageByOperation(): Record<string, BillingPeriod> {
        const operations = ['chat', 'completion', 'nextEdit', 'instruction'];
        const result: Record<string, BillingPeriod> = {};

        for (const operation of operations) {
            const records = this.usageRecords.filter(r => r.operation === operation);
            if (records.length > 0) {
                const sortedRecords = [...records].sort((a, b) => 
                    a.timestamp.localeCompare(b.timestamp)
                );
                const start = sortedRecords[0].timestamp.split('T')[0];
                const end = sortedRecords[sortedRecords.length - 1].timestamp.split('T')[0];
                result[operation] = this.aggregateRecords(records, start, end);
            } else {
                const today = new Date().toISOString().split('T')[0];
                result[operation] = {
                    startDate: today,
                    endDate: today,
                    totalCost: 0,
                    totalTokens: 0,
                    recordCount: 0
                };
            }
        }

        return result;
    }

    public addCostAlert(alert: Omit<CostAlert, 'id' | 'currentAmount' | 'triggered'>): void {
        const newAlert: CostAlert = {
            ...alert,
            id: this.generateAlertId(),
            currentAmount: 0,
            triggered: false
        };

        this.costAlerts.push(newAlert);
        this.saveCostAlerts();
        Logger.info(`Cost alert added: ${alert.type} threshold $${alert.threshold}`);
    }

    public removeCostAlert(alertId: string): void {
        this.costAlerts = this.costAlerts.filter(alert => alert.id !== alertId);
        this.saveCostAlerts();
        Logger.info(`Cost alert removed: ${alertId}`);
    }

    public getCostAlerts(): CostAlert[] {
        return [...this.costAlerts];
    }

    public exportUsageData(startDate?: string, endDate?: string): string {
        let records = this.usageRecords;

        if (startDate || endDate) {
            records = records.filter(record => {
                const recordDate = record.timestamp.split('T')[0];
                if (startDate && recordDate < startDate) return false;
                if (endDate && recordDate > endDate) return false;
                return true;
            });
        }

        const exportData = {
            exportDate: new Date().toISOString(),
            dateRange: {
                start: startDate || 'all',
                end: endDate || 'all'
            },
            summary: this.aggregateRecords(records, startDate || '', endDate || ''),
            records: records
        };

        return JSON.stringify(exportData, null, 2);
    }

    public clearUsageData(olderThanDays?: number): void {
        if (olderThanDays) {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
            const cutoffString = cutoffDate.toISOString();

            this.usageRecords = this.usageRecords.filter(record => 
                record.timestamp >= cutoffString
            );
        } else {
            this.usageRecords = [];
        }

        this.saveUsageRecords();
        Logger.info(`Usage data cleared${olderThanDays ? ` (older than ${olderThanDays} days)` : ''}`);
    }

    private aggregateRecords(records: UsageRecord[], startDate: string, endDate: string): BillingPeriod {
        const totalCost = records.reduce((sum, record) => sum + record.cost.totalCost, 0);
        const totalTokens = records.reduce((sum, record) => sum + record.usage.totalTokens, 0);

        return {
            startDate,
            endDate,
            totalCost,
            totalTokens,
            recordCount: records.length
        };
    }

    private checkCostAlerts(newRecord: UsageRecord): void {
        for (const alert of this.costAlerts) {
            let currentAmount = 0;

            switch (alert.type) {
                case 'daily':
                    currentAmount = this.getDailyUsage().totalCost;
                    break;
                case 'weekly':
                    currentAmount = this.getWeeklyUsage().totalCost;
                    break;
                case 'monthly':
                    currentAmount = this.getMonthlyUsage().totalCost;
                    break;
                case 'total':
                    currentAmount = this.getTotalUsage().totalCost;
                    break;
            }

            alert.currentAmount = currentAmount;

            if (currentAmount >= alert.threshold && !alert.triggered) {
                alert.triggered = true;
                alert.lastTriggered = new Date().toISOString();
                this.triggerCostAlert(alert);
            }
        }

        this.saveCostAlerts();
    }

    private triggerCostAlert(alert: CostAlert): void {
        const message = `Cost Alert: ${alert.type} spending has reached $${alert.currentAmount.toFixed(2)} (threshold: $${alert.threshold})`;
        
        vscode.window.showWarningMessage(message, 'View Usage', 'Settings')
            .then(selection => {
                if (selection === 'View Usage') {
                    vscode.commands.executeCommand('vscode-gemini-pro.openBillingPanel');
                } else if (selection === 'Settings') {
                    vscode.commands.executeCommand('vscode-gemini-pro.openSettingsPanel');
                }
            });

        Logger.warn(`Cost alert triggered: ${message}`);
    }

    private getWeekStart(): string {
        const now = new Date();
        const dayOfWeek = now.getDay();
        const start = new Date(now);
        start.setDate(now.getDate() - dayOfWeek);
        return start.toISOString().split('T')[0];
    }

    private getWeekEnd(weekStart: string): string {
        const start = new Date(weekStart);
        const end = new Date(start);
        end.setDate(start.getDate() + 6);
        return end.toISOString().split('T')[0];
    }

    private getMonthEnd(month: string): string {
        const [year, monthNum] = month.split('-').map(Number);
        const lastDay = new Date(year, monthNum, 0).getDate();
        return `${month}-${lastDay.toString().padStart(2, '0')}`;
    }

    private generateRecordId(): string {
        return `usage_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    private generateAlertId(): string {
        return `alert_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    private setupPeriodicCleanup(): void {
        // Reset daily alerts at midnight
        const now = new Date();
        const tomorrow = new Date(now);
        tomorrow.setDate(now.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        
        const msUntilMidnight = tomorrow.getTime() - now.getTime();
        
        setTimeout(() => {
            this.resetDailyAlerts();
            // Set up daily interval
            setInterval(() => this.resetDailyAlerts(), 24 * 60 * 60 * 1000);
        }, msUntilMidnight);
    }

    private resetDailyAlerts(): void {
        for (const alert of this.costAlerts) {
            if (alert.type === 'daily') {
                alert.triggered = false;
                alert.currentAmount = 0;
            }
        }
        this.saveCostAlerts();
    }

    private async loadUsageRecords(): Promise<void> {
        try {
            const stored = this.context.globalState.get<UsageRecord[]>('geminiPro.usageRecords', []);
            this.usageRecords = stored;
            Logger.debug(`Loaded ${this.usageRecords.length} usage records`);
        } catch (error) {
            Logger.error('Error loading usage records:', error);
            this.usageRecords = [];
        }
    }

    private async saveUsageRecords(): Promise<void> {
        try {
            await this.context.globalState.update('geminiPro.usageRecords', this.usageRecords);
        } catch (error) {
            Logger.error('Error saving usage records:', error);
        }
    }

    private async loadCostAlerts(): Promise<void> {
        try {
            const stored = this.context.globalState.get<CostAlert[]>('geminiPro.costAlerts', []);
            this.costAlerts = stored;
            Logger.debug(`Loaded ${this.costAlerts.length} cost alerts`);
        } catch (error) {
            Logger.error('Error loading cost alerts:', error);
            this.costAlerts = [];
        }
    }

    private async saveCostAlerts(): Promise<void> {
        try {
            await this.context.globalState.update('geminiPro.costAlerts', this.costAlerts);
        } catch (error) {
            Logger.error('Error saving cost alerts:', error);
        }
    }

    public dispose(): void {
        this.saveUsageRecords();
        this.saveCostAlerts();
    }
}
